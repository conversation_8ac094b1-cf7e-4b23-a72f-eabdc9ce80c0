// 测试学生API的JSON格式转换
// 这个文件用于验证字段名转换是否正确

// 模拟前端表单数据（大写字段名）
const frontendData = {
  id: 1,
  name: "张三",
  Chinese: 85,
  Math: 92,
  English: 78,
  Sum: 255
}

// 模拟addStudent函数的转换逻辑
function transformToBackend(data) {
  const transformedData = {
    id: data.id,
    name: data.name,
    chinese: data.Chinese,
    math: data.Math,
    english: data.English,
    sum: data.Sum
  }
  return transformedData
}

// 模拟后端返回的数据（小写字段名）
const backendData = {
  id: 1,
  name: "张三",
  chinese: 85,
  math: 92,
  english: 78,
  sum: 255
}

// 模拟transformFromBackend函数的转换逻辑
function transformFromBackend(data) {
  if (Array.isArray(data)) {
    return data.map(item => ({
      ...item,
      Chinese: item.chinese,
      Math: item.math,
      English: item.english,
      Sum: item.sum
    }))
  } else {
    return {
      ...data,
      Chinese: data.chinese,
      Math: data.math,
      English: data.english,
      Sum: data.sum
    }
  }
}

// 测试转换
console.log("原始前端数据:", frontendData)
console.log("转换为后端格式:", transformToBackend(frontendData))
console.log("后端返回数据:", backendData)
console.log("转换为前端格式:", transformFromBackend(backendData))

// 测试数组转换
const backendArray = [backendData, { ...backendData, id: 2, name: "李四" }]
console.log("后端数组数据:", backendArray)
console.log("转换为前端数组格式:", transformFromBackend(backendArray))
