import request from '@/utils/request'

// 查询学生成绩列表
export function listStudent(query) {
  return request({
    url: '/student/student/list',
    method: 'get',
    params: query
  })
}

// 查询学生成绩详细
export function getStudent(id) {
  return request({
    url: '/student/student/' + id,
    method: 'get'
  })
}

// 新增学生成绩
export function addStudent(data) {
  // 转换字段名为小写
  const transformedData = {
    id: data.id,
    name: data.name,
    chinese: data.Chinese,
    math: data.Math,
    english: data.English,
    sum: data.Sum
  }

  return request({
    url: '/student/student',
    method: 'post',
    data: data
  })
}

// 修改学生成绩
export function updateStudent(data) {
  return request({
    url: '/student/student',
    method: 'put',
    data: data
  })
}

// 删除学生成绩
export function delStudent(id) {
  return request({
    url: '/student/student/' + id,
    method: 'delete'
  })
}
